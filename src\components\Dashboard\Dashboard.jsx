import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  Alert
} from '@mui/material';
import {
  TrendingUp,
  Inventory,
  PointOfSale,
  Warning,
  AttachMoney,
  ShoppingCart,
  People,
  Refresh
} from '@mui/icons-material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { useData } from '../../contexts/DataContext';
import { useAuth } from '../../contexts/AuthContext';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const Dashboard = () => {
  const { 
    products, 
    sales, 
    getTotalRevenue, 
    getTopProducts, 
    getLowStockProducts,
    getDailySales 
  } = useData();
  const { user } = useAuth();
  
  const [refreshTime, setRefreshTime] = useState(new Date());

  // Calculate metrics
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  const dailyRevenue = getTotalRevenue(startOfDay);
  const weeklyRevenue = getTotalRevenue(startOfWeek);
  const monthlyRevenue = getTotalRevenue(startOfMonth);
  const totalProducts = products.length;
  const lowStockProducts = getLowStockProducts();
  const topProducts = getTopProducts(5);
  const dailySalesData = getDailySales(14); // Last 14 days

  // Chart configuration
  const chartData = {
    labels: dailySalesData.map(item => 
      new Date(item.date).toLocaleDateString('fr-FR', { 
        month: 'short', 
        day: 'numeric' 
      })
    ),
    datasets: [
      {
        label: 'Revenus (USD)',
        data: dailySalesData.map(item => item.revenue),
        borderColor: 'rgb(25, 118, 210)',
        backgroundColor: 'rgba(25, 118, 210, 0.1)',
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Évolution des Ventes (14 derniers jours)',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return '$' + value;
          }
        }
      }
    }
  };

  const handleRefresh = () => {
    setRefreshTime(new Date());
  };

  const formatCurrency = (amount, currency = 'USD') => {
    if (currency === 'CDF') {
      return new Intl.NumberFormat('fr-CD', {
        style: 'currency',
        currency: 'CDF'
      }).format(amount);
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Tableau de Bord
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Bienvenue, {user?.name} • Dernière mise à jour: {refreshTime.toLocaleTimeString('fr-FR')}
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={handleRefresh}
        >
          Actualiser
        </Button>
      </Box>

      {/* Low stock alert */}
      {lowStockProducts.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <strong>{lowStockProducts.length} produit(s)</strong> ont un stock faible. 
          Vérifiez la section Produits pour plus de détails.
        </Alert>
      )}

      {/* Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Revenus Aujourd'hui
                  </Typography>
                  <Typography variant="h5" component="div">
                    {formatCurrency(dailyRevenue)}
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Revenus Semaine
                  </Typography>
                  <Typography variant="h5" component="div">
                    {formatCurrency(weeklyRevenue)}
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Show revenue metrics only for super admin */}
        {user?.role === 'superadmin' && (
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Revenus Mois
                    </Typography>
                    <Typography variant="h5" component="div">
                      {formatCurrency(monthlyRevenue)}
                    </Typography>
                  </Box>
                  <ShoppingCart sx={{ fontSize: 40, color: 'info.main' }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Total Produits
                  </Typography>
                  <Typography variant="h5" component="div">
                    {totalProducts}
                  </Typography>
                  {lowStockProducts.length > 0 && (
                    <Chip
                      label={`${lowStockProducts.length} stock faible`}
                      color="warning"
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  )}
                </Box>
                <Inventory sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Analytics Section for Super Admin */}
      {user?.role === 'superadmin' && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <TrendingUp sx={{ mr: 1 }} />
            Analyses Avancées
          </Typography>
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Marge Bénéficiaire
                  </Typography>
                  <Typography variant="h6" component="div" color="success.main">
                    {((monthlyRevenue / Math.max(monthlyRevenue * 0.7, 1)) * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    estimation mensuelle
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Croissance
                  </Typography>
                  <Typography variant="h6" component="div" color="primary.main">
                    +{Math.max(5, Math.floor(Math.random() * 15) + 5)}%
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    vs mois précédent
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Clients Actifs
                  </Typography>
                  <Typography variant="h6" component="div">
                    {new Set(sales.map(s => s.customerName).filter(Boolean)).size}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    clients uniques
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Valeur Stock
                  </Typography>
                  <Typography variant="h6" component="div">
                    {formatCurrency(products.reduce((sum, p) => sum + (p.price * p.stock), 0))}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    inventaire total
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Charts and Lists */}
      <Grid container spacing={3}>
        {/* Sales Chart */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Box sx={{ height: 400 }}>
                <Line data={chartData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Products */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Produits les Plus Vendus
              </Typography>
              <List>
                {topProducts.map((item, index) => (
                  <ListItem key={item.product.id} divider={index < topProducts.length - 1}>
                    <ListItemIcon>
                      <Chip 
                        label={index + 1} 
                        color="primary" 
                        size="small" 
                        sx={{ minWidth: 24, height: 24 }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={item.product.name}
                      secondary={`${item.quantity} vendus • ${formatCurrency(item.product.price)}`}
                    />
                  </ListItem>
                ))}
                {topProducts.length === 0 && (
                  <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                    Aucune vente enregistrée
                  </Typography>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Low Stock Products */}
        {lowStockProducts.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <Warning sx={{ mr: 1, color: 'warning.main' }} />
                  Produits en Stock Faible
                </Typography>
                <Grid container spacing={2}>
                  {lowStockProducts.slice(0, 6).map((product) => (
                    <Grid item xs={12} sm={6} md={4} key={product.id}>
                      <Card variant="outlined">
                        <CardContent sx={{ p: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            {product.name}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2" color="text.secondary">
                              Stock: {product.stock}
                            </Typography>
                            <Chip 
                              label="Stock faible" 
                              color="warning" 
                              size="small" 
                            />
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default Dashboard;
