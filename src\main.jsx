import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { frFR } from '@mui/material/locale';
import App from './App';

// Create custom theme with French locale
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1976d2',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#fafafa',
          borderRight: '1px solid #e0e0e0',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: 8,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 6,
        },
      },
    },
  },
}, frFR);

// Function to hide loading screen
const hideLoadingScreen = () => {
  console.log('Hiding loading screen...');
  const loading = document.getElementById('loading');
  if (loading) {
    loading.style.opacity = '0';
    loading.style.transition = 'opacity 0.5s ease-out';
    setTimeout(() => {
      loading.style.display = 'none';
      console.log('Loading screen hidden successfully');
    }, 500);
  } else {
    console.warn('Loading element not found');
  }
};

// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('React Error Boundary caught an error:', error, errorInfo);
    // Hide loading screen if there's an error
    hideLoadingScreen();
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          fontFamily: 'Roboto, sans-serif',
          backgroundColor: '#f5f5f5',
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <h1 style={{ color: '#d32f2f' }}>Erreur de l'application</h1>
          <p>Une erreur s'est produite lors du chargement de l'application.</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Recharger la page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// App wrapper component to handle loading
const AppWrapper = () => {
  const [appReady, setAppReady] = React.useState(false);

  React.useEffect(() => {
    // Add a small delay to ensure everything is properly initialized
    const timer = setTimeout(() => {
      console.log('React app mounted successfully');
      setAppReady(true);
      hideLoadingScreen();
    }, 500); // Increased delay to ensure proper initialization

    // Fallback timeout to ensure loading screen is hidden
    const fallbackTimer = setTimeout(() => {
      console.warn('Fallback: Forcing app to show after timeout');
      setAppReady(true);
      hideLoadingScreen();
    }, 2000);

    return () => {
      clearTimeout(timer);
      clearTimeout(fallbackTimer);
    };
  }, []);

  // Show a minimal loading state while React is initializing
  if (!appReady) {
    return null; // Let the HTML loading screen handle this
  }

  return (
    <ErrorBoundary>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <App />
        </ThemeProvider>
      </BrowserRouter>
    </ErrorBoundary>
  );
};

// Fallback timeout to hide loading screen if React takes too long
setTimeout(() => {
  const loading = document.getElementById('loading');
  if (loading && loading.style.display !== 'none') {
    console.warn('Loading screen timeout reached, forcing hide');
    hideLoadingScreen();
  }
}, 5000);

// Render the app with error boundary
try {
  ReactDOM.createRoot(document.getElementById('root')).render(
    <React.StrictMode>
      <AppWrapper />
    </React.StrictMode>
  );
} catch (error) {
  console.error('Failed to render React app:', error);
  // Fallback: hide loading screen after timeout if React fails
  setTimeout(hideLoadingScreen, 2000);
}
