import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Typography,
  Chip
} from '@mui/material';
import {
  Dashboard,
  Inventory,
  PointOfSale,
  Assessment,
  Analytics,
  People,
  Store
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Sidebar = ({ drawerWidth, mobileOpen, onDrawerToggle, userRole }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission, user } = useAuth();

  const menuItems = [
    {
      text: 'Tableau de Bord',
      icon: <Dashboard />,
      path: '/dashboard',
      permission: 'view_dashboard'
    },
    {
      text: 'Produits',
      icon: <Inventory />,
      path: '/products',
      permission: 'view_products'
    },
    {
      text: 'Ventes',
      icon: <PointOfSale />,
      path: '/sales',
      permission: 'manage_sales'
    },
    {
      text: 'Rapports',
      icon: <Assessment />,
      path: '/reports',
      permission: 'view_reports',
      roles: ['superadmin'] // Only super admin can access reports
    },
    {
      text: 'Analyses',
      icon: <Analytics />,
      path: '/analytics',
      permission: 'view_analytics',
      roles: ['superadmin'] // Only super admin can access analytics
    },
    {
      text: 'Utilisateurs',
      icon: <People />,
      path: '/users',
      permission: 'manage_users'
    }
  ];

  const getRoleColor = (role) => {
    switch (role) {
      case 'superadmin':
        return 'error';
      case 'admin':
        return 'warning';
      case 'employee':
        return 'info';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role) => {
    switch (role) {
      case 'superadmin':
        return 'Super Admin';
      case 'admin':
        return 'Administrateur';
      case 'employee':
        return 'Employé';
      default:
        return role;
    }
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo and Title */}
      <Box sx={{ p: 3, textAlign: 'center', borderBottom: '1px solid #e0e0e0' }}>
        <Store sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
        <Typography variant="h6" component="div" fontWeight="bold">
          Maboutique
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Gestion de Boutique
        </Typography>
        <Box sx={{ mt: 2 }}>
          <Chip 
            label={getRoleLabel(userRole)}
            color={getRoleColor(userRole)}
            size="small"
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ pt: 2 }}>
          {menuItems.map((item) => {
            if (!hasPermission(item.permission)) {
              return null;
            }

            // Check role-specific restrictions
            if (item.roles && !item.roles.includes(user?.role)) {
              return null;
            }

            const isActive = location.pathname === item.path;
            
            return (
              <ListItem key={item.text} disablePadding sx={{ px: 2, mb: 0.5 }}>
                <ListItemButton
                  onClick={() => {
                    navigate(item.path);
                    if (onDrawerToggle) onDrawerToggle();
                  }}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: isActive ? 'primary.main' : 'transparent',
                    color: isActive ? 'white' : 'text.primary',
                    '&:hover': {
                      backgroundColor: isActive ? 'primary.dark' : 'action.hover',
                    },
                    '& .MuiListItemIcon-root': {
                      color: isActive ? 'white' : 'text.secondary',
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={item.text}
                    primaryTypographyProps={{
                      fontSize: '0.9rem',
                      fontWeight: isActive ? 600 : 400
                    }}
                  />
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Version 1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          © 2024 Maboutique
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box
      component="nav"
      sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
    >
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={onDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
          },
        }}
      >
        {drawer}
      </Drawer>
      
      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
          },
        }}
        open
      >
        {drawer}
      </Drawer>
    </Box>
  );
};

export default Sidebar;
